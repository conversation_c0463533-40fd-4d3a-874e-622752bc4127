"""
产品服务层（员工入驻专用）
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from typing import List, Dict, Any, Optional
import hashlib
import httpx
from datetime import datetime
from utils.log_util import logger
from utils.common_util import CommonUtil
from module_admin.entity.vo.staff_registration_vo import (
    StaffRegistrationRequestModel,
    StaffRegistrationResponseModel
)
from module_admin.entity.vo.staff_list_vo import (
    StaffListRequestModel,
    StaffListResponseModel,
    StaffItemModel,
    StaffStatisticsModel
)
from module_admin.dao.product_dao import ProductDao
from exceptions.exception import BusinessException, QueryException, ValidationException


class ProductService:
    """产品服务类（员工入驻专用）"""

    # 外部接口配置
    INVENTORY_API_URL = "https://clientapi.jingangai.cn/inventory/generateStaffInventory"
    HTTP_TIMEOUT = 30  # 30秒超时

    @classmethod
    async def _call_generate_staff_inventory(
        cls,
        service_staff_id: int,
        service_staff_uuid: str
    ) -> Dict[str, Any]:
        """
        调用外部接口生成员工库存

        :param service_staff_id: 员工ID
        :param service_staff_uuid: 员工UUID
        :return: 接口调用结果
        """
        try:
            # 准备请求参数（二选一即可，这里两个都传递）
            request_data = {
                "service_staff_uuid": service_staff_uuid
            }

            logger.info(f"开始调用外部接口生成员工库存，参数: {request_data}")

            async with httpx.AsyncClient(timeout=cls.HTTP_TIMEOUT) as client:
                response = await client.post(
                    cls.INVENTORY_API_URL,
                    json=request_data,
                    headers={
                        "Content-Type": "application/json"
                    }
                )

                # 检查HTTP状态码
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"外部接口调用成功，响应: {result}")
                    return {
                        "success": True,
                        "data": result,
                        "message": "员工库存生成成功"
                    }
                else:
                    logger.error(f"外部接口调用失败，状态码: {response.status_code}, 响应: {response.text}")
                    return {
                        "success": False,
                        "data": None,
                        "message": f"外部接口调用失败，状态码: {response.status_code}"
                    }

        except httpx.TimeoutException:
            logger.error(f"外部接口调用超时，URL: {cls.INVENTORY_API_URL}")
            return {
                "success": False,
                "data": None,
                "message": "外部接口调用超时"
            }
        except Exception as e:
            logger.error(f"外部接口调用异常: {str(e)}")
            return {
                "success": False,
                "data": None,
                "message": f"外部接口调用异常: {str(e)}"
            }

    @classmethod
    async def get_products_service(
        cls,
        db: AsyncSession,
        company_uuid: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取产品列表服务（公开接口专用）

        获取指定公司的启用产品，用于员工入驻时的产品选择
        必须提供有效的公司UUID，否则返回空列表

        :param db: 数据库会话
        :param company_uuid: 公司UUID，必须提供，只返回该公司的产品
        :return: 产品列表
        :raises DatabaseException: 数据库操作异常
        """
        try:
            # 必须提供有效的公司UUID，否则返回空列表
            if not company_uuid:
                logger.warning("未提供公司UUID，返回空产品列表")
                return []

            # 修复SQL：在使用DISTINCT时，ORDER BY字段必须在SELECT中
            # 方案1：将create_time添加到SELECT中，然后在Python中去重
            # 只返回指定公司的产品
            sql = """
                SELECT
                    p.id,
                    p.product_name as name,
                    COALESCE(p.service_skill_name, p.service_skill_main_name, '未分类') as category,
                    p.create_time
                FROM product p
                WHERE p.product_status = 1
                AND p.is_delete = 0
                AND p.company_uuid = :company_uuid
                ORDER BY p.create_time DESC
                LIMIT 200
            """
            result = await db.execute(text(sql), {"company_uuid": company_uuid})
            rows = result.fetchall()

            # 在Python中进行去重，保持最新的记录
            # 由于SQL已经按create_time DESC排序，第一次遇到的就是最新的
            seen_products = set()
            products = []

            for row in rows:
                # 使用产品名称和分类组合作为去重键
                product_key = f"{row.name}_{row.category}"

                if product_key not in seen_products:
                    product = {
                        "id": str(row.id),
                        "name": row.name,
                        "category": row.category
                    }
                    products.append(product)
                    seen_products.add(product_key)

                    # 移除硬编码的100个产品限制
                    # 允许返回所有去重后的产品，确保员工可以选择公司的所有产品

            logger.info(f"获取产品列表成功，原始数量: {len(rows)}, 去重后数量: {len(products)}")
            return products

        except Exception as e:
            logger.error(f"获取产品列表失败: {str(e)}")
            from exceptions.exception import DatabaseException
            raise DatabaseException(message=f"获取产品列表失败: {str(e)}")

    @classmethod
    async def get_products_by_company(
        cls,
        db: AsyncSession,
        company_id: str
    ) -> List[Dict[str, Any]]:
        """
        根据公司ID获取产品列表（简化版）

        :param db: 数据库会话
        :param company_id: 公司ID
        :return: 产品列表
        :raises DatabaseException: 数据库操作异常
        :raises ValidationException: 参数验证异常
        """
        if not company_id:
            from exceptions.exception import ValidationException
            raise ValidationException(message="公司ID不能为空")

        sql = """
            SELECT id, product_name, service_skill_main_name, service_skill_name
            FROM product
            WHERE company_uuid = :company_id
            AND product_status = 1
            AND is_delete = 0
            ORDER BY create_time DESC
        """
        result = await db.execute(text(sql), {"company_id": company_id})
        rows = result.fetchall()

        products = []
        for row in rows:
            product = {
                "id": str(row.id),
                "name": row.product_name,
                "category": row.service_skill_name or row.service_skill_main_name or "未分类"
            }
            products.append(product)

        logger.info(f"获取公司 {company_id} 的产品成功，数量: {len(products)}")
        return products

    @staticmethod
    async def get_purchasable_products_service(
        db: AsyncSession,
        company_uuid: str,
        page: int = 1,
        size: int = 20,
        search_keyword: Optional[str] = None,
        service_skill_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取可购买产品列表服务

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param page: 页码
        :param size: 每页数量
        :param search_keyword: 搜索关键词
        :param service_skill_id: 服务技能ID筛选
        :return: 产品列表数据
        """
        try:
            # 调用DAO层获取产品列表
            product_list, total = await ProductDao.get_purchasable_products_by_company(
                db, company_uuid, page, size, search_keyword, service_skill_id
            )

            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "total": total,
                "list": product_list,
                "has_more": (page * size) < total,
                "total_pages": (total + size - 1) // size
            }

            logger.info(f"获取公司 {company_uuid} 可购买产品列表成功，总数: {total}")
            return result

        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取可购买产品列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取可购买产品列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取可购买产品列表失败: {str(e)}")

    @staticmethod
    async def get_purchasable_product_categories_service(
        db: AsyncSession,
        company_uuid: str
    ) -> List[Dict[str, Any]]:
        """
        获取可购买产品分类列表服务

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 分类列表
        """
        try:
            # 调用DAO层获取分类列表
            categories = await ProductDao.get_purchasable_product_categories(db, company_uuid)

            logger.info(f"获取公司 {company_uuid} 可购买产品分类成功，分类数: {len(categories)}")
            return categories

        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取可购买产品分类查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取可购买产品分类服务异常: {str(e)}")
            raise BusinessException(message=f"获取可购买产品分类失败: {str(e)}")

    @staticmethod
    async def get_purchasable_products_with_categories_service(
        db: AsyncSession,
        company_uuid: str,
        page: int = 1,
        size: int = 20,
        category: Optional[str] = None,
        keyword: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取可购买产品列表和分类信息（合并接口，优化性能）

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param page: 页码
        :param size: 每页数量
        :param category: 分类筛选
        :param keyword: 关键词搜索
        :return: 包含产品列表、分类列表和分页信息的字典
        """
        try:
            # 并发获取分类和产品数据
            import asyncio

            # 创建并发任务
            categories_task = ProductDao.get_purchasable_product_categories(db, company_uuid)
            products_task = ProductDao.get_purchasable_products_by_company(
                db, company_uuid, page, size, search_keyword=keyword
            )

            # 并发执行
            categories, (products, total) = await asyncio.gather(
                categories_task,
                products_task
            )

            # 构建响应数据
            result = {
                "products": products,
                "categories": categories,
                "pagination": {
                    "page": page,
                    "size": size,
                    "total": total,
                    "pages": (total + size - 1) // size
                }
            }

            logger.info(f"获取公司 {company_uuid} 可购买产品和分类成功，产品数: {len(products)}, 分类数: {len(categories)}")
            return result

        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取可购买产品和分类查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取可购买产品和分类服务异常: {str(e)}")
            raise BusinessException(message=f"获取可购买产品和分类失败: {str(e)}")

    @classmethod
    async def register_staff(
        cls,
        db: AsyncSession,
        staff_data: StaffRegistrationRequestModel,
        current_user_id: str,
        company_id: str,
        store_id: str,
        invitation_code: Optional[str] = None,
        inviter_type: Optional[str] = None
    ) -> StaffRegistrationResponseModel:
        """
        员工入驻服务

        :param db: 数据库会话
        :param staff_data: 员工入驻数据
        :param current_user_id: 当前用户ID
        :param company_id: 公司ID
        :param store_id: 门店ID
        :param invitation_code: 邀请码
        :param inviter_type: 邀请人类型 ('internal_user' 或 'service_staff')
        :return: 员工入驻响应
        :raises ValidationException: 参数验证异常
        :raises DatabaseException: 数据库操作异常
        """
        if not current_user_id:
            from exceptions.exception import ValidationException
            raise ValidationException(message="当前用户ID不能为空")

        if not company_id:
            from exceptions.exception import ValidationException
            raise ValidationException(message="公司ID不能为空")

        if not store_id:
            from exceptions.exception import ValidationException
            raise ValidationException(message="门店ID不能为空")

        # 生成员工UUID（不带横线）
        staff_uuid = CommonUtil.get_uuid_without_hyphen()

        # 生成身份证MD5
        id_number_md5 = hashlib.md5(staff_data.id_number.encode()).hexdigest()

        try:
            # 1. 检查在同一公司下是否已存在相同手机号的员工
            duplicate_check_sql = """
                SELECT id, real_name, mobile, status
                FROM service_staff
                WHERE company_id = :company_id AND mobile = :mobile AND is_delete = '0'
                LIMIT 1
            """
            duplicate_result = await db.execute(text(duplicate_check_sql), {
                "company_id": company_id,
                "mobile": staff_data.mobile
            })
            existing_staff = duplicate_result.fetchone()

            if existing_staff:
                from exceptions.exception import ValidationException
                raise ValidationException(
                    message=f"该手机号({staff_data.mobile})在当前公司下已存在员工记录，员工姓名：{existing_staff.real_name}，请勿重复注册"
                )

            # 2. 获取当前用户的门店信息
            store_info_sql = """
                SELECT s.name as store_name, s.store_uuid as store_uuid, u.name as user_name
                FROM store s
                LEFT JOIN internal_user u ON u.store_id = s.id
                WHERE s.id = :store_id AND u.uuid = :user_uuid
                LIMIT 1
            """
            store_result = await db.execute(text(store_info_sql), {
                "store_id": store_id,
                "user_uuid": current_user_id
            })
            store_info = store_result.fetchone()

            store_name = store_info.store_name if store_info else ""
            store_uuid = store_info.store_uuid if store_info else ""
            user_name = store_info.user_name if store_info else "系统管理员"

            # 3. 插入service_staff表，根据邀请人类型存储邀请码到不同字段
            if inviter_type == 'service_staff':
                # 员工邀请：将邀请码存储到staff_share_code字段
                staff_sql = """
                    INSERT INTO service_staff (
                        uuid, company_id, store_id, store_name, store_uuid, real_name, user_name, nick_name, mobile, sex, age,
                        avatar, address, status, work_type, is_delete, staff_share_code,
                        created_by, created_by_name, create_time, update_time
                    ) VALUES (
                        :uuid, :company_id, :store_id, :store_name, :store_uuid, :real_name, :user_name, :nick_name, :mobile, :sex, :age,
                        :avatar, :address, '1', '1', '0', :staff_share_code,
                        :created_by, :created_by_name, NOW(), NOW()
                    )
                """
                staff_params = {
                    "uuid": staff_uuid,
                    "company_id": company_id,
                    "store_id": store_id,
                    "store_name": store_name,
                    "store_uuid": store_uuid,
                    "real_name": staff_data.real_name,
                    "user_name": staff_data.real_name,  # user_name设置为real_name
                    "nick_name": staff_data.real_name,  # nick_name设置为real_name
                    "mobile": staff_data.mobile,
                    "sex": staff_data.sex,
                    "age": staff_data.age or "0",
                    "avatar": "",  # avatar不设置值
                    "address": staff_data.address,
                    "staff_share_code": invitation_code,  # 员工邀请码存储到staff_share_code字段
                    "created_by": current_user_id,
                    "created_by_name": user_name
                }
            else:
                # 门店管理员邀请：将邀请码存储到invitation_code字段
                staff_sql = """
                    INSERT INTO service_staff (
                        uuid, company_id, store_id, store_name, store_uuid, real_name, user_name, nick_name, mobile, sex, age,
                        avatar, address, status, work_type, is_delete, invitation_code,
                        created_by, created_by_name, create_time, update_time
                    ) VALUES (
                        :uuid, :company_id, :store_id, :store_name, :store_uuid, :real_name, :user_name, :nick_name, :mobile, :sex, :age,
                        :avatar, :address, '1', '1', '0', :invitation_code,
                        :created_by, :created_by_name, NOW(), NOW()
                    )
                """
                staff_params = {
                    "uuid": staff_uuid,
                    "company_id": company_id,
                    "store_id": store_id,
                    "store_name": store_name,
                    "store_uuid": store_uuid,
                    "real_name": staff_data.real_name,
                    "user_name": staff_data.real_name,  # user_name设置为real_name
                    "nick_name": staff_data.real_name,  # nick_name设置为real_name
                    "mobile": staff_data.mobile,
                    "sex": staff_data.sex,
                    "age": staff_data.age or "0",
                    "avatar": "",  # avatar不设置值
                    "address": staff_data.address,
                    "invitation_code": invitation_code,  # 门店管理员邀请码存储到invitation_code字段
                    "created_by": current_user_id,
                    "created_by_name": user_name
                }

            result = await db.execute(text(staff_sql), staff_params)
            staff_id = result.lastrowid

            logger.info(f"成功插入service_staff表，staff_id: {staff_id}, 邀请人类型: {inviter_type}, 邀请码: {invitation_code}")

            # 4. 插入service_staff_ext表
            ext_sql = """
                INSERT INTO service_staff_ext (
                    staff_id, id_number, birthday, id_number_md5, lng, lat,
                    start_time, end_time, rest_days, native_place, nation, marriage_status,
                    education_background, height, weight, family_address,
                    medical_history, province_id, province_name, area_id,
                    area_name, address_desc, travel_tool, bank_name,
                    bank_card_holder, bank_card_number, bank_card_photo,
                    emergency_contact_name, emergency_contact_relation,
                    emergency_contact_phone, emergency_contact_address,
                    id_card_people_photo, id_card_address,
                    created_by, created_at, updated_at
                ) VALUES (
                    :staff_id, :id_number, :birthday, :id_number_md5, :lng, :lat,
                    :start_time, :end_time, :rest_days, :native_place, :nation, :marriage_status,
                    :education_background, :height, :weight, :family_address,
                    :medical_history, :province_id, :province_name, :area_id,
                    :area_name, :address_desc, :travel_tool, :bank_name,
                    :bank_card_holder, :bank_card_number, :bank_card_photo,
                    :emergency_contact_name, :emergency_contact_relation,
                    :emergency_contact_phone, :emergency_contact_address,
                    :id_card_people_photo, :id_card_address,
                    :created_by, NOW(), NOW()
                )
            """

            ext_params = {
                "staff_id": staff_id,
                "id_number": staff_data.id_number,
                "birthday": staff_data.birthday or "0",
                "id_number_md5": id_number_md5,
                "lng": staff_data.lng,
                "lat": staff_data.lat,
                "start_time": staff_data.start_time or 8,  # 默认8时
                "end_time": staff_data.end_time or 18,     # 默认18时
                "rest_days": staff_data.rest_days or "",   # 休息日数组，如"1,2,3"
                "native_place": staff_data.native_place,
                "nation": staff_data.nation,
                "marriage_status": staff_data.marriage_status or "0",
                "education_background": staff_data.education_background or "0",
                "height": staff_data.height or "0",
                "weight": staff_data.weight or "0",
                "family_address": staff_data.family_address,
                "medical_history": staff_data.medical_history,
                "province_id": staff_data.province_id,
                "province_name": staff_data.province_name,
                "area_id": staff_data.area_id or "0",
                "area_name": staff_data.area_name,
                "address_desc": staff_data.address_desc,
                "travel_tool": staff_data.travel_tool,
                "bank_name": staff_data.bank_name,
                "bank_card_holder": staff_data.bank_card_holder,
                "bank_card_number": staff_data.bank_card_number,
                "bank_card_photo": staff_data.bank_card_photo,
                "emergency_contact_name": staff_data.emergency_contact_name,
                "emergency_contact_relation": staff_data.emergency_contact_relation or "0",
                "emergency_contact_phone": staff_data.emergency_contact_phone,
                "emergency_contact_address": staff_data.emergency_contact_address,
                "id_card_people_photo": staff_data.avatar,  # 身份证人像页URL
                "id_card_address": staff_data.id_card_address,  # 身份证地址
                "created_by": current_user_id
            }

            await db.execute(text(ext_sql), ext_params)
            logger.info(f"成功插入service_staff_ext表，staff_id: {staff_id}")

            # 5. 插入service_product表（员工产品关联）
            if staff_data.product_ids:
                # 首先验证所有产品都属于当前公司
                # 使用参数化查询避免SQL注入
                placeholders = ','.join([f':product_id_{i}' for i in range(len(staff_data.product_ids))])
                product_validation_sql = f"""
                    SELECT id, product_name, company_uuid
                    FROM product
                    WHERE id IN ({placeholders}) AND product_status = 1 AND is_delete = 0
                """

                # 构建参数字典
                validation_params = {f'product_id_{i}': product_id for i, product_id in enumerate(staff_data.product_ids)}
                validation_result = await db.execute(text(product_validation_sql), validation_params)
                valid_products = validation_result.fetchall()

                # 检查产品数量是否匹配
                if len(valid_products) != len(staff_data.product_ids):
                    invalid_ids = set(staff_data.product_ids) - {p.id for p in valid_products}
                    from exceptions.exception import ValidationException
                    raise ValidationException(message=f"产品ID无效或已删除: {invalid_ids}")

                # 检查所有产品是否属于当前公司
                invalid_company_products = [p for p in valid_products if p.company_uuid != company_id]
                if invalid_company_products:
                    invalid_names = [p.product_name for p in invalid_company_products]
                    from exceptions.exception import ValidationException
                    raise ValidationException(message=f"产品不属于当前公司: {invalid_names}")

                # 验证通过，插入产品关联
                for product_id in staff_data.product_ids:
                    product_sql = """
                        INSERT INTO service_product (staff_id, productid, store_uuid, company_uuid, create_time, update_time)
                        VALUES (:staff_id, :product_id, :store_uuid, :company_uuid, NOW(), NOW())
                    """
                    await db.execute(text(product_sql), {
                        "staff_id": staff_id,
                        "product_id": product_id,
                        "store_uuid": store_uuid,
                        "company_uuid": company_id
                    })

                logger.info(f"成功插入service_product表，staff_id: {staff_id}, 产品数量: {len(staff_data.product_ids)}, store_uuid: {store_uuid}, company_uuid: {company_id}")

            # 提交事务
            await db.commit()

            # 构造响应
            response = StaffRegistrationResponseModel(
                staff_id=staff_id,
                uuid=staff_uuid,
                real_name=staff_data.real_name,
                mobile=staff_data.mobile,
                status="1",
                create_time=datetime.now()
            )

            logger.info(f"员工入驻成功，staff_id: {staff_id}, real_name: {staff_data.real_name}")

            # 员工入驻成功后，调用外部接口生成员工库存
            # 注意：这里使用异步调用，但不影响主流程的成功状态
            try:
                inventory_result = await cls._call_generate_staff_inventory(
                    service_staff_id=staff_id,
                    service_staff_uuid=staff_uuid
                )

                if inventory_result["success"]:
                    logger.info(f"员工库存生成成功，staff_id: {staff_id}")
                else:
                    logger.warning(f"员工库存生成失败，但不影响员工入驻，staff_id: {staff_id}, 错误: {inventory_result['message']}")

            except Exception as e:
                # 外部接口调用失败不应该影响员工入驻的成功状态
                logger.error(f"调用外部接口生成员工库存时发生异常，但不影响员工入驻，staff_id: {staff_id}, 异常: {str(e)}")

            return response

        except Exception as e:
            # 回滚事务
            await db.rollback()
            logger.error(f"员工入驻失败: {str(e)}")
            raise

    @staticmethod
    async def get_staff_list(
        db: AsyncSession,
        request_data: StaffListRequestModel,
        current_user_id: str,
        store_uuid: str
    ) -> StaffListResponseModel:
        """
        获取员工列表

        Args:
            db: 数据库会话
            request_data: 查询请求参数
            current_user_id: 当前用户ID
            store_uuid: 门店UUID

        Returns:
            员工列表响应数据
        """
        try:
            logger.info(f"用户 {current_user_id} 查询员工列表，门店: {store_uuid}")

            # 构建基础查询条件
            where_conditions = ["s.store_uuid = :store_uuid", "s.is_delete = '0'"]
            params = {"store_uuid": store_uuid}

            # 添加状态筛选
            if request_data.status is not None:
                where_conditions.append("s.status = :status")
                params["status"] = request_data.status

            # 添加工作类型筛选
            if request_data.work_type is not None:
                where_conditions.append("s.work_type = :work_type")
                params["work_type"] = request_data.work_type

            # 添加关键词搜索
            if request_data.keyword:
                where_conditions.append("(s.real_name LIKE :keyword OR s.mobile LIKE :keyword)")
                params["keyword"] = f"%{request_data.keyword}%"

            where_clause = " AND ".join(where_conditions)

            # 查询员工列表（分页）
            offset = (request_data.page - 1) * request_data.page_size
            staff_list_sql = f"""
                SELECT
                    s.id, s.uuid, s.real_name, s.mobile, s.sex, s.age, s.avatar,
                    s.status, s.star_level, s.address, s.service_cnt, s.create_time
                FROM service_staff s
                WHERE {where_clause}
                ORDER BY s.create_time DESC
                LIMIT :limit OFFSET :offset
            """

            params.update({
                "limit": request_data.page_size,
                "offset": offset
            })

            result = await db.execute(text(staff_list_sql), params)
            staff_rows = result.fetchall()

            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM service_staff s
                WHERE {where_clause}
            """

            count_params = {k: v for k, v in params.items() if k not in ['limit', 'offset']}
            count_result = await db.execute(text(count_sql), count_params)
            total_count = count_result.fetchone()[0]

            # 查询统计数据
            stats_sql = """
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as normal,
                    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as frozen
                FROM service_staff s
                WHERE s.store_uuid = :store_uuid AND s.is_delete = '0'
            """

            stats_result = await db.execute(text(stats_sql), {"store_uuid": store_uuid})
            stats_row = stats_result.fetchone()

            # 构建员工列表
            staff_list = []
            for row in staff_rows:
                # 查询员工技能（从service_product表获取）
                skills_sql = """
                    SELECT p.product_name
                    FROM service_product sp
                    LEFT JOIN product p ON sp.productid = p.id
                    WHERE sp.staff_id = :staff_id
                      AND p.product_name IS NOT NULL
                      AND p.product_name != ''
                      AND p.is_delete = 0
                """

                skills_result = await db.execute(text(skills_sql), {"staff_id": row.id})
                skills_rows = skills_result.fetchall()
                skills = [skill_row[0] for skill_row in skills_rows if skill_row[0]]

                staff_item = StaffItemModel(
                    id=row.id,
                    uuid=row.uuid,
                    real_name=row.real_name or "",
                    mobile=row.mobile,
                    sex=row.sex,
                    age=row.age,
                    avatar=row.avatar,
                    status=row.status or "1",
                    star_level=row.star_level,
                    address=row.address,
                    service_cnt=row.service_cnt,
                    create_time=row.create_time,
                    skills=skills,
                    work_description=f"专业服务人员，擅长{', '.join(skills[:2]) if skills else '多项服务'}"
                )
                staff_list.append(staff_item)

            # 构建统计数据
            statistics = StaffStatisticsModel(
                total=stats_row.total or 0,
                normal=stats_row.normal or 0,
                frozen=stats_row.frozen or 0,
                idle=0  # 暂时默认为0，后续可以根据业务逻辑计算
            )

            # 计算总页数
            total_pages = (total_count + request_data.page_size - 1) // request_data.page_size

            # 构建响应数据
            response = StaffListResponseModel(
                list=staff_list,
                total=total_count,
                page=request_data.page,
                page_size=request_data.page_size,
                total_pages=total_pages,
                statistics=statistics
            )

            logger.info(f"员工列表查询成功，共 {total_count} 条记录，当前页 {len(staff_list)} 条")
            return response

        except Exception as e:
            logger.error(f"查询员工列表失败: {str(e)}")
            raise e

    @classmethod
    async def get_store_products_service(
        cls,
        db: AsyncSession,
        company_uuid: str,
        store_uuid: str = None
    ) -> Dict[str, Any]:
        """
        获取门店产品列表服务（支持门店级别状态控制）

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param store_uuid: 门店UUID（可选，用于门店级别状态控制）
        :return: 门店产品列表数据
        """
        try:
            # 验证公司UUID
            if not company_uuid:
                raise ValidationException(message="公司UUID不能为空")

            # 调用DAO层获取门店产品列表（支持门店级别状态控制）
            product_list = await ProductDao.get_products_by_company_uuid(db, company_uuid, store_uuid)

            # 构建返回数据
            result = {
                "total": len(product_list),
                "list": product_list
            }

            logger.info(f"获取公司 {company_uuid} 门店产品列表成功，总数: {len(product_list)}")
            return result

        except ValidationException as e:
            # 参数验证异常，直接向上传递
            logger.error(f"获取门店产品列表参数验证异常: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取门店产品列表查询异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"获取门店产品列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取门店产品列表失败: {str(e)}")

    @classmethod
    async def batch_update_products_status_service(
        cls,
        db: AsyncSession,
        company_uuid: str,
        product_ids: List[int],
        status: int,
        store_uuid: str = None
    ) -> Dict[str, Any]:
        """
        批量更新产品状态服务（支持门店级别黑名单控制）

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param product_ids: 产品ID列表
        :param status: 目标状态（0-下架，1-上架）
        :param store_uuid: 门店UUID（可选，用于门店级别状态控制）
        :return: 更新结果数据
        """
        try:
            # 验证参数
            if not company_uuid:
                raise ValidationException(message="公司UUID不能为空")
            if not product_ids:
                raise ValidationException(message="产品ID列表不能为空")
            if status not in [0, 1]:
                raise ValidationException(message="状态值必须为0（下架）或1（上架）")

            # 调用DAO层批量更新产品状态（支持门店级别黑名单控制）
            updated_count = await ProductDao.batch_update_product_status(
                db, company_uuid, product_ids, status, store_uuid
            )

            # 构建返回数据
            result = {
                "updated_count": updated_count,
                "total_requested": len(product_ids),
                "status": status,
                "status_name": "上架" if status == 1 else "下架"
            }

            logger.info(f"批量更新产品状态成功，公司: {company_uuid}, 更新数量: {updated_count}/{len(product_ids)}, 状态: {status}")
            return result

        except ValidationException as e:
            # 参数验证异常，直接向上传递
            logger.error(f"批量更新产品状态参数验证异常: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"批量更新产品状态查询异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"批量更新产品状态服务异常: {str(e)}")
            raise BusinessException(message=f"批量更新产品状态失败: {str(e)}")

    @classmethod
    async def get_product_detail_service(
        cls,
        db: AsyncSession,
        product_id: int,
        company_uuid: str
    ) -> Dict[str, Any]:
        """
        获取产品详情服务

        :param db: 数据库会话
        :param product_id: 产品ID
        :param company_uuid: 公司UUID
        :return: 产品详情数据
        """
        try:
            # 验证参数
            if not product_id:
                raise ValidationException(message="产品ID不能为空")
            if not company_uuid:
                raise ValidationException(message="公司UUID不能为空")

            # 调用DAO层获取产品详情
            product = await ProductDao.get_product_detail_by_id(db, product_id, company_uuid)

            if not product:
                raise ResourceNotFoundException(message=f"未找到ID为{product_id}的产品或该产品不属于当前公司")

            logger.info(f"获取产品详情成功，产品ID: {product_id}, 公司: {company_uuid}")
            return product

        except ValidationException as e:
            logger.error(f"获取产品详情参数验证失败: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            logger.error(f"产品不存在: {e.message}")
            raise e
        except QueryException as e:
            logger.error(f"获取产品详情查询失败: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"获取产品详情服务异常: {str(e)}")
            raise BusinessException(message=f"获取产品详情失败: {str(e)}")

    @staticmethod
    async def create_product_service(
        db: AsyncSession,
        company_uuid: str,
        store_uuid: Optional[str],
        current_user_id: str,
        product_data
    ) -> Dict[str, Any]:
        """
        创建新产品服务

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param store_uuid: 门店UUID
        :param current_user_id: 当前用户ID
        :param product_data: 产品数据
        :return: 创建结果
        """
        try:
            import uuid
            from datetime import datetime

            logger.info(f"创建产品服务开始，产品名称: {product_data.product_name}, 公司: {company_uuid}")
            logger.info(f"接收到的SKU信息: {product_data.sku_info}")

            # 生成产品UUID
            product_uuid = str(uuid.uuid4())

            # 获取主服务技能名称
            main_skill_name = await ProductService._get_main_skill_name(
                db, product_data.service_skill_main_id
            )

            # 处理主图ID
            main_image_id = product_data.main_image_id or 1  # 如果没有主图ID，使用默认值1

            # 处理详情图ID列表
            detail_image_ids = product_data.detail_image_ids or []

            # 构建产品数据
            insert_sql = """
                INSERT INTO product (
                    uuid, product_name, company_uuid, img_id, serve_type_name,
                    service_skill_id, service_skill_name, service_skill_main_id, service_skill_main_name,
                    type, type_name, product_status, is_delete, min_number, max_number,
                    buy_notes, buy_agreement, details, rating_weight, proximity_weight,
                    business_hours, create_time, update_time, op_user_name
                ) VALUES (
                    :uuid, :product_name, :company_uuid, :img_id, :serve_type_name,
                    :service_skill_id, :service_skill_name, :service_skill_main_id, :service_skill_main_name,
                    :type, :type_name, :product_status, :is_delete, :min_number, :max_number,
                    :buy_notes, :buy_agreement, :details, :rating_weight, :proximity_weight,
                    :business_hours, :create_time, :update_time, :op_user_name
                )
            """

            params = {
                "uuid": product_uuid,
                "product_name": product_data.product_name,
                "company_uuid": company_uuid,
                "img_id": main_image_id,
                "serve_type_name": "上门服务",
                "service_skill_id": product_data.service_skill_id,
                "service_skill_name": product_data.service_skill_name,
                "service_skill_main_id": product_data.service_skill_main_id,
                "service_skill_main_name": main_skill_name,
                "type": "service",
                "type_name": "服务产品",
                "product_status": 1,  # 默认上架
                "is_delete": 0,
                "min_number": product_data.min_number,
                "max_number": product_data.max_number,
                "buy_notes": product_data.buy_notes or "",
                "buy_agreement": product_data.buy_agreement or "",
                "details": ",".join(map(str, detail_image_ids)) if detail_image_ids else "",
                "rating_weight": 10,  # 固定值
                "proximity_weight": 50,  # 固定值
                "business_hours": product_data.business_hours or "08:00-18:00",  # 可预约时间
                "create_time": datetime.now(),
                "update_time": datetime.now(),
                "op_user_name": current_user_id
            }

            result = await db.execute(text(insert_sql), params)

            # 获取插入的产品ID
            product_id_query = """
                SELECT id FROM product WHERE uuid = :uuid
            """
            product_id_result = await db.execute(text(product_id_query), {"uuid": product_uuid})
            product_row = product_id_result.fetchone()
            product_id = product_row[0] if product_row else None

            logger.info(f"获取到的产品ID: {product_id}")

            if not product_id:
                raise BusinessException(message="获取产品ID失败")

            # 创建对应的SKU记录
            logger.info(f"开始创建SKU记录，产品ID: {product_id}, SKU信息: {product_data.sku_info}")

            sku_insert_sql = """
                INSERT INTO product_sku (
                    productid, name, now_price, vip_price, duration,
                    type_price_unit, define_commission, commission_type,
                    create_time, update_time
                ) VALUES (
                    :productid, :name, :now_price, :vip_price, :duration,
                    :type_price_unit, :define_commission, :commission_type,
                    :create_time, :update_time
                )
            """

            sku_params = {
                "productid": product_id,
                "name": product_data.sku_info.name,
                "now_price": product_data.sku_info.now_price,
                "vip_price": product_data.sku_info.vip_price,
                "duration": product_data.sku_info.duration,
                "type_price_unit": product_data.sku_info.type_price_unit,
                "define_commission": product_data.sku_info.define_commission,
                "commission_type": product_data.sku_info.commission_type,
                "create_time": datetime.now(),
                "update_time": datetime.now()
            }

            logger.info(f"SKU插入参数: {sku_params}")

            sku_result = await db.execute(text(sku_insert_sql), sku_params)
            await db.commit()

            logger.info(f"产品和SKU创建成功: {product_data.product_name}, UUID: {product_uuid}, Product ID: {product_id}")

            return {
                "product_id": product_id,
                "product_uuid": product_uuid,
                "product_name": product_data.product_name,
                "main_image_id": main_image_id,
                "detail_image_ids": detail_image_ids,
                "sku_info": {
                    "name": product_data.sku_info.name,
                    "now_price": product_data.sku_info.now_price,
                    "vip_price": product_data.sku_info.vip_price
                }
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"创建产品失败: {str(e)}")
            logger.error(f"异常详情: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"创建产品失败: {str(e)}")

    @staticmethod
    async def _create_image_record(db: AsyncSession, image_url: str, remark: str, created_by: str) -> int:
        """创建图片文件记录"""
        try:
            from datetime import datetime

            # 创建主表记录
            insert_main_sql = """
                INSERT INTO file_main (remark, created_by, created_at)
                VALUES (:remark, :created_by, :created_at)
            """

            result = await db.execute(text(insert_main_sql), {
                "remark": remark,
                "created_by": created_by,
                "created_at": datetime.now()
            })

            main_id = result.lastrowid

            # 创建子表记录
            insert_item_sql = """
                INSERT INTO file_item (main_id, file_name, file_type, file_size, file_url, created_at)
                VALUES (:main_id, :file_name, :file_type, :file_size, :file_url, :created_at)
            """

            # 从URL中提取文件名
            file_name = image_url.split('/')[-1] if '/' in image_url else 'image.jpg'

            await db.execute(text(insert_item_sql), {
                "main_id": main_id,
                "file_name": file_name,
                "file_type": "image/jpeg",
                "file_size": 0,  # 外部上传的图片无法获取大小
                "file_url": image_url,
                "created_at": datetime.now()
            })

            return main_id

        except Exception as e:
            logger.error(f"创建图片文件记录失败: {str(e)}")
            return 1  # 返回默认ID，避免创建产品失败

    @staticmethod
    async def _get_main_skill_name(db: AsyncSession, main_skill_id: str) -> str:
        """获取主服务技能名称"""
        try:
            sql = """
                SELECT service_skill_name
                FROM service_skill
                WHERE id = :skill_id AND status = 1
            """
            result = await db.execute(text(sql), {"skill_id": main_skill_id})
            row = result.fetchone()
            return row.service_skill_name if row else "未知分类"
        except Exception as e:
            logger.warning(f"获取主服务技能名称失败: {str(e)}")
            return "未知分类"

    @classmethod
    async def update_product_service(
        cls,
        db: AsyncSession,
        product_id: int,
        company_uuid: str,
        current_user_id: str,
        product_data
    ) -> Dict[str, Any]:
        """
        更新产品服务

        :param db: 数据库会话
        :param product_id: 产品ID
        :param company_uuid: 公司UUID
        :param current_user_id: 当前用户ID
        :param product_data: 产品数据
        :return: 更新结果
        """
        try:
            from datetime import datetime

            logger.info(f"更新产品服务开始，产品ID: {product_id}, 产品名称: {product_data.product_name}, 公司: {company_uuid}")

            # 首先验证产品是否存在且属于当前公司，同时获取原有的购买须知和购买协议
            check_sql = """
                SELECT id, uuid, buy_notes, buy_agreement FROM product
                WHERE id = :product_id AND company_uuid = :company_uuid AND is_delete = 0
            """
            check_result = await db.execute(text(check_sql), {
                "product_id": product_id,
                "company_uuid": company_uuid
            })
            product_row = check_result.fetchone()

            if not product_row:
                raise BusinessException(message="产品不存在或无权限修改")

            product_uuid = product_row[1]
            # 保留原有的购买须知和购买协议
            original_buy_notes = product_row[2] or ""
            original_buy_agreement = product_row[3] or ""

            # 获取主服务技能名称
            main_skill_name = await cls._get_main_skill_name(
                db, product_data.service_skill_main_id
            )

            # 处理主图ID
            main_image_id = product_data.main_image_id or 1

            # 处理详情图ID列表
            detail_image_ids = product_data.detail_image_ids or []

            # 更新产品主表
            update_sql = """
                UPDATE product SET
                    product_name = :product_name,
                    img_id = :img_id,
                    service_skill_id = :service_skill_id,
                    service_skill_name = :service_skill_name,
                    service_skill_main_id = :service_skill_main_id,
                    service_skill_main_name = :service_skill_main_name,
                    min_number = :min_number,
                    max_number = :max_number,
                    buy_notes = :buy_notes,
                    buy_agreement = :buy_agreement,
                    business_hours = :business_hours,
                    update_time = :update_time,
                    op_user_name = :op_user_name
                WHERE id = :product_id AND company_uuid = :company_uuid
            """

            update_params = {
                "product_id": product_id,
                "company_uuid": company_uuid,
                "product_name": product_data.product_name,
                "img_id": main_image_id,
                "service_skill_id": product_data.service_skill_id,
                "service_skill_name": product_data.service_skill_name,
                "service_skill_main_id": product_data.service_skill_main_id,
                "service_skill_main_name": main_skill_name,
                "min_number": product_data.min_number,
                "max_number": product_data.max_number,
                "buy_notes": original_buy_notes,  # 保持原有的购买须知
                "buy_agreement": original_buy_agreement,  # 保持原有的购买协议
                "business_hours": product_data.business_hours or "08:00-18:00",
                "update_time": datetime.now(),
                "op_user_name": current_user_id
            }

            await db.execute(text(update_sql), update_params)

            # 更新SKU信息
            logger.info(f"开始更新SKU记录，产品ID: {product_id}, SKU信息: {product_data.sku_info}")

            # 先删除现有SKU记录
            delete_sku_sql = "DELETE FROM product_sku WHERE productid = :product_id"
            await db.execute(text(delete_sku_sql), {"product_id": product_id})

            # 插入新的SKU记录
            sku_insert_sql = """
                INSERT INTO product_sku (
                    productid, name, now_price, vip_price, duration,
                    type_price_unit, define_commission, commission_type,
                    create_time, update_time
                ) VALUES (
                    :productid, :name, :now_price, :vip_price, :duration,
                    :type_price_unit, :define_commission, :commission_type,
                    :create_time, :update_time
                )
            """

            sku_params = {
                "productid": product_id,
                "name": product_data.sku_info.name,
                "now_price": product_data.sku_info.now_price,
                "vip_price": product_data.sku_info.vip_price,
                "duration": product_data.sku_info.duration,
                "type_price_unit": product_data.sku_info.type_price_unit,
                "define_commission": product_data.sku_info.define_commission,
                "commission_type": product_data.sku_info.commission_type,
                "create_time": datetime.now(),
                "update_time": datetime.now()
            }

            await db.execute(text(sku_insert_sql), sku_params)

            # 处理详情图（如果有的话）
            # 详情图ID以逗号分隔的字符串格式存储在details字段中
            if detail_image_ids:
                # 将详情图ID列表转换为逗号分隔的字符串
                details_str = ','.join(str(img_id) for img_id in detail_image_ids)

                # 更新product表的details字段
                update_details_sql = """
                    UPDATE product SET
                        details = :details,
                        update_time = :update_time
                    WHERE id = :product_id AND company_uuid = :company_uuid
                """

                await db.execute(text(update_details_sql), {
                    "product_id": product_id,
                    "company_uuid": company_uuid,
                    "details": details_str,
                    "update_time": datetime.now()
                })

                logger.info(f"更新产品详情图成功，图片ID: {details_str}")

            # 提交事务
            await db.commit()

            logger.info(f"产品更新成功，产品ID: {product_id}, 产品UUID: {product_uuid}")

            return {
                "product_id": product_id,
                "product_uuid": product_uuid,
                "product_name": product_data.product_name,
                "main_image_id": main_image_id,
                "detail_image_ids": detail_image_ids
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"更新产品失败: {str(e)}")
            if isinstance(e, BusinessException):
                raise e
            else:
                raise BusinessException(message=f"更新产品失败: {str(e)}")


